import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import cesium from 'vite-plugin-cesium'
// import legacy from '@vitejs/plugin-legacy'
export default defineConfig({
  base: './',
  plugins: [
    vue(),
    cesium(),
    // legacy({
    //   targets: ['defaults', 'not IE 11'],
    //   renderLegacyChunks: true,
    //   modernPolyfills: true,
    // }),
    // legacy({
    //   targets: ['defaults', 'not IE 11'],
    // }),
  ],
  // build: {
  //   target: ['edge90', 'chrome52', 'firefox90', 'safari15'], // 适配低版本浏览器
  // },
  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS'
    }
  },
  server: {
    port: '9000', //端口
    // host: "localhost",//ip地址例如*************
    host: true,
    open: true, //服务启动时自动在浏览器中打开应用
    // 反向代理配置
    // proxy: {
    //   //配置多个代理
    //   '/dev-api': {
    //     target: 'https://xxxx.com/', //例子:http://*************:8080 或测试服务器https://xxxx.com
    //     changeOrigin: true, ///设置访问目标地址允许跨域
    //     rewrite: (p) => p.replace(/^\/dev-api/, ''),
    //   },
    //   '/prod-api': {
    //     target: 'https://xxxx.com/',
    //     changeOrigin: true, ///设置访问目标地址允许跨域
    //     rewrite: (p) => p.replace(/^\/prod-api/, ''),
    //   },
    // },
  },
})
