{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode develop", "dev:prod": "vite --mode production", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.3.6", "echarts": "^5.4.2", "element-plus": "^2.3.3", "pinyin-pro": "^3.26.0", "three": "^0.121.0", "v-scale-screen": "^2.0.0", "vanta": "^0.5.24", "vue": "^3.2.47", "vue-router": "^4.1.6", "vue3-scale-box": "^0.1.6", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^4.1.0", "cesium": "^1.104.0", "sass": "^1.26.5", "sass-loader": "^7.0.0", "vite": "^4.3.1", "vite-plugin-cesium": "^1.2.22"}}