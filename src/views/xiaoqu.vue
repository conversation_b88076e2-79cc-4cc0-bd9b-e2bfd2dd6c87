<!-- 

<template>
  <div id="cesiumContainer"></div>
</template>
<script >

  import * as Cesium from 'cesium';
  import { Cesium3DTileset, Viewer, WebMapTileServiceImageryProvider } from "cesium";
  import { onMounted } from 'vue';
  // import url from './assets/test01/tileset.json'
  export default {
    name: "MapComponent",
    setup() {
      //初始化地图
      let viewer;

      function initMap() {
        const tianDiTuToken = '7b56038c276128a86a5b946404bf4df4'
        const mapOption = {
          url: `http://t0.tianditu.com/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=${tianDiTuToken}`,
          layer: "tdtBasicLayer",
          style: "default",
          format: "image/jpeg",
          tileMatrixSetID: "GoogleMapsCompatible",
          maximumLevel: 18
        };
        const imgProvider = new WebMapTileServiceImageryProvider(mapOption);

        const viewerOption = {
          animation: false,//是否创建动画小器件，左下角仪表
          baseLayerPicker: false,//是否显示图层选择器
          fullscreenButton: false,//是否显示全屏按钮
          geocoder: false,//是否显示geocoder小器件，右上角查询按钮
          homeButton: false,//是否显示Home按钮
          infoBox: false,//是否显示信息框
          sceneModePicker: false,//是否显示3D/2D选择器
          scene3DOnly: false,//如果设置为true，则所有几何图形以3D模式绘制以节约GPU资源
          selectionIndicator: false,//是否显示选取指示器组件
          timeline: false,//是否显示时间轴
          navigationHelpButton: false,//是否显示右上角的帮助按钮
          baselLayerPicker: false,// 将图层选择的控件关掉，才能添加其他影像数据
          shadows: true,//是否显示背影
          shouldAnimate: true,
          imageryProvider: imgProvider,
        }

        viewer = new Viewer("cesiumContainer", viewerOption);
        // 添加中文注记图层
        viewer.imageryLayers.addImageryProvider(
          new WebMapTileServiceImageryProvider({
            url: `http://t0.tianditu.com/cia_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default.jpg&tk=${tianDiTuToken}`,
            layer: "tdtAnnoLayer",
            style: "default",
            format: "image/jpeg",
            tileMatrixSetID: "GoogleMapsCompatible",
            show: false,
          }));
        //优化项--关闭相关特效
        viewer.scene.debugShowFramesPerSecond = true;//显示fps
        viewer.scene.moon.show = false;//月亮
        viewer.scene.fog.enabled = false;//雾
        viewer.scene.sun.show = false;//太阳
        viewer.scene.skyBox.show = false;//天空盒
        viewer.resolutionScale = 1.0;//画面细度，默认值为1.0
        //去除版权信息
        viewer._cesiumWidget._creditContainer.style.display = "none";
      }

      //加载倾斜摄影图像
      function init3Dtiles() {
        const tileSet = new Cesium3DTileset({
          url: "../public/test01/tileset.json",
          maximumMemoryUsage: 100,//不可设置太高，目标机子空闲内存值以内，防止浏览器过于卡
          maximumScreenSpaceError: 32,//用于驱动细节细化级别的最大屏幕空间错误;较高的值可提供更好的性能，但视觉质量较低。
          maximumNumberOfLoadedTiles: 1000,  //最大加载瓦片个数
          shadows: false,//是否显示阴影
          skipLevelOfDetail: true,// 确定是否应在遍历期间应用详细级别跳过(默认false)
          baseScreenSpaceError: 1024,//When skipLevelOfDetailis true，在跳过详细级别之前必须达到的屏幕空间错误(默认1024)
          skipScreenSpaceErrorFactor: 16,// 定义要跳过的最小屏幕空间错误的乘数。与 一起使用skipLevels来确定要加载哪些图块(默认16)
          skipLevels: 1,//skipLevelOfDetail是true 一个常量，定义了加载图块时要跳过的最小级别数。为 0 时，不跳过任何级别。与 一起使用skipScreenSpaceErrorFactor来确定要加载哪些图块。(默认1)
          immediatelyLoadDesiredLevelOfDetail: false,//当skipLevelOfDetail是时true，只会下载满足最大屏幕空间错误的图块。忽略跳过因素，只加载所需的图块(默认false)
          loadSiblings: false,// 如果为true则不会在已加载完概况房屋后，自动从中心开始超清化房屋 --- 何时确定在遍历期间skipLevelOfDetail是否true始终下载可见瓦片的兄弟姐妹(默认false)
          cullWithChildrenBounds: true,//是否使用子边界体积的并集来剔除瓦片（默认true）
          dynamicScreenSpaceError: true,//减少距离相机较远的图块的屏幕空间错误(默认false)
          dynamicScreenSpaceErrorDensity: 0.00278,//数值加大，能让周边加载变快 --- 用于调整动态屏幕空间误差的密度，类似于雾密度(默认0.00278)
          dynamicScreenSpaceErrorFactor: 4.0,// 用于增加计算的动态屏幕空间误差的因素(默认4.0)
          dynamicScreenSpaceErrorHeightFalloff: 0.25//密度开始下降的瓦片集高度的比率(默认0.25)
        });

        viewer.scene.primitives.add(tileSet);
        viewer.zoomTo(tileSet);

        //加载后调整倾斜摄影的位置、角度等参数
        tileSet.readyPromise.then((tileset) => {
          update3dtilesMaxtrix(tileset);
        });
      }

      //更新倾斜摄影位置
      function update3dtilesMaxtrix(tileSet) {
        //调整参数
        let params = {
          tx: 113.06265738392063, //模型中心X轴坐标（经度，单位：十进制度）
          ty: 22.646803971034342,  //模型中心Y轴坐标（纬度，单位：十进制度）
          tz: 40,  //模型中心Z轴坐标（高程，单位：米）
          rx: 0,  //X轴（经度）方向旋转角度（单位：度）
          ry: 0,  //Y轴（纬度）方向旋转角度（单位：度）
          rz: 2,   //Z轴（高程）方向旋转角度（单位：度）
          scale: 1.30//缩放比例
        };
        //旋转
        const mx = Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(params.rx));
        const my = Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(params.ry));
        const mz = Cesium.Matrix3.fromRotationZ(Cesium.Math.toRadians(params.rz));
        const rotationX = Cesium.Matrix4.fromRotationTranslation(mx);
        const rotationY = Cesium.Matrix4.fromRotationTranslation(my);
        const rotationZ = Cesium.Matrix4.fromRotationTranslation(mz);
        //平移
        const position = Cesium.Cartesian3.fromDegrees(params.tx, params.ty, params.tz);
        const m = Cesium.Transforms.eastNorthUpToFixedFrame(position);
        //旋转、平移矩阵相乘
        Cesium.Matrix4.multiply(m, rotationX, m);
        Cesium.Matrix4.multiply(m, rotationY, m);
        Cesium.Matrix4.multiply(m, rotationZ, m);
        //比例缩放
        const scale = Cesium.Matrix4.fromUniformScale(params.scale)
        Cesium.Matrix4.multiply(m, scale, m)
        console.log("矩阵m:", m);
        //赋值给tileset
        tileSet._root.transform = m;
      }

      onMounted(() => {
        initMap();
        init3Dtiles()
      })
    }
  }
</script>
<style scoped>
  #cesiumContainer {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }
</style> -->

<!-- 测试页面 无效无效！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！ -->
<template>
  <div class="goback" @click="$router.replace('/wangge')">&emsp;返回</div>
  <div class="qiu" @click="showAll">
    <img src="/zhankai.png" v-if="$store.state.show.zhan" alt="" />
    <img src="/shousuo.png" v-else alt="" />
  </div>
  <div class="biao">A街区网格</div>
  <div id="cesiumContainer"></div>
  <div class="left" v-if="!$store.state.show.zhan">
    <div class="one">
      <div class="title">
        <span>街区网格基础数据</span>
      </div>
      <div class="one_dis">
        <span class="dis_" v-for="(item, index) in data.list" :key="index">
          <span class="top">{{ item.value1 }}</span>
          <img src="../../public/di02.png" alt="" />
          <span class="bottom">{{ item.value2 }}</span>
        </span>
      </div>
    </div>
    <div class="two">
      <div class="title"><span>人口数据情况</span></div>
      <div class="two_dis"></div>
    </div>
    <div class="three">
      <div class="title"><span>年龄结构情况</span></div>
    </div>
  </div>
  <div class="right" v-if="!$store.state.show.zhan">
    <div class="one">
      <div class="title"><span>经营情况</span></div>
      <div class="one_dis"></div>
    </div>
    <div class="two">
      <div class="title"><span>特殊人员情况</span></div>
      <div class="two_dis"></div>
    </div>
  </div>
</template>
<script setup>
import * as Cesium from 'cesium'
import { onMounted, onUnmounted, ref, reactive } from 'vue'
import { useStore } from 'vuex' // 引入useStore 方法
import { useRouter } from 'vue-router'

const router = useRouter()
const store = useStore() // 该方法用于返回store 实例
let zhan = ref(true)
let handler = ref(null)
let viewer = ref(null)
let data = reactive({
  data: [
    {
      lng: 105.815985,
      lat: 32.44494,
      infoId: 1,
      text: '则南第一小区',
    },
    {
      lng: 105.818,
      lat: 32.45,
      infoId: 2,
      text: '则南第二小区',
    },
    {
      lng: 105.828,
      lat: 32.457,
      infoId: 3,
      text: '则南第三小区',
    },
    {
      lng: 105.815,
      lat: 32.441,
      infoId: 4,
      text: '则南第四小区',
    },
    {
      lng: 105.83,
      lat: 32.456,
      infoId: 5,
      text: '则南第五小区',
    },
    {
      lng: 105.8234,
      lat: 32.449,
      infoId: 6,
      text: '则南第六小区',
    },
  ],
})
onMounted(() => {
  // const viewer = new Cesium.Viewer('cesiumContainer');
  // let custom = new Cesium.ArcGisMapServerImageryProvider({
  //   url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer'
  // })
  viewer.value = new Cesium.Viewer('cesiumContainer', {
    // 稳定版
    geocoder: false, //是否显示地名查找控件
    sceneModePicker: false, //是否显示投影方式控件
    navigationHelpButton: false, //是否显示帮助信息控件
    baseLayerPicker: true, //是否显示图层选择控件
    homeButton: false, //是否显示Home按钮
    fullscreenButton: true, //是否显示全屏按钮
    timeline: false, //时间轴控件
    animation: false, //动画控件
    shouldAnimate: true,
    infoBox: false, //初始化不弹出弹出框
    selectionIndicator: false, //初始化不选中
    baseLayerPicker: false,
    // imageryProvider: custom,
    terrainProvider: Cesium.createWorldTerrain({
      requestVertexNormals: true,
      requestWaterMask: true,
    }),
    // sceneOptions: {
    //   requestRenderMode: true,
    //   heightReference: 5000
    // }

    imageryProvider: new Cesium.UrlTemplateImageryProvider({
      url: 'https://webst02.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}',
      layer: 'tdtVecBasicLayer',
      style: 'default',
      format: 'image/png',
      tileMatrixSetID: 'GooglMapsCompatible',
      show: false,
    }),
    // 新版
    // terrainProvider: Cesium.createWorldTerrain(
    //   {
    //     requestVertexNormals: true,
    //     requestWaterMask: true
    //   }
    // ), // 地形数据
    // baseLayerPicker: false,
    // geocoder: false,
    // homeButton: false,
    // sceneModePicker: false,
    // navigationHelpButton: false,
    // animation: false,
    // timeline: false,
    // fullscreenButton: false,
    // skyAtmosphere: false,
    // skyBox: false,
    // shouldAnimate: true,
    // contextOptions: {
    //   webgl: {
    //     alpha: false,
    //     depth: true,
    //     stencil: true,
    //     antialias: true,
    //     premultipliedAlpha: true,
    //     preserveDrawingBuffer: true,
    //     failIfMajorPerformanceCaveat: false
    //   }
    // },
    // scene3DOnly: true,
    // sceneMode: Cesium.SceneMode.SCENE3D,
    // sceneModePicker: false,
    // maximumScreenSpaceError: 8,
    // globe: false,
    // targetFrameRate: 30,
    // orderIndependentTranslucency: true,
    // automaticallyTrackDataSourceClocks: false,
    // showRenderLoopErrors: false,
    // highDynamicRange: true,
    // useDefaultRenderLoop: true,
    // shadows: true,
    // // 将场景高度设置为5000米
    // sceneOptions: {
    //   requestRenderMode: true,
    //   heightReference: 5000
    // }
  })
  // viewer.value.imageryLayers.addImageryProvider(new Cesium.UrlTemplateImageryProvider({
  //   url: "http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8",
  //   layer: "tdtAnnoLayer",
  //   style: "default",
  //   format: "image/jpeg",
  //   tileMatrixSetID: "GoogleMapsCompatible"
  // }));
  viewer.value.cesiumWidget.creditContainer.style.display = 'none' //去cesium logo水印 或 css
  viewer.value.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(105.82076, 32.450511, 5000.0),
    // destination: Cesium.Cartesian3.fromDegrees(113.318977, 23.114155, 1500.0),
    // orientation: {
    //   // heading: Cesium.Math.toRadians(),
    //   // pitch: Cesium.Math.toRadians(-45),
    // }
  })

  // const tile = new Cesium.Cesium3DTileset(
  //   {
  //     url: '/test01/tileset.json',
  //   }
  // )
  // const tile = new Cesium.Cesium3DTileset(
  //  {url:'https://assets.ion.cesium.com/75343/tileset.json?v=1'}
  // )
  // const tile = await Cesium.Cesium3DTileset.fromIonAssetId(75343)
  // console.log(tile);
  // var city = this.viewer.scene.primitives.add(tile)
  // this.viewer.scene.globe.depthTestAgainstTerrain = true;
  // this.viewer.zoomTo(
  //   tile,
  //   // new Cesium.HeadingPitchRange(
  //   //   0.0,
  //   //   -0.5,
  //   //   tile.boundingSphere.radius * 1.0
  //   // )
  // );
  viewer.value.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray([105.818, 32.45, 105.8234, 32.449, 105.815, 32.441, 105.811, 32.443]),
      material: Cesium.Color.fromCssColorString('#f63d3f').withAlpha(0.15),
      classificationType: Cesium.ClassificationType.BOTH, // classificationType: ClassificationType.TERRAIN,
      HeightReference: Cesium.HeightReference.NONE,
      clampToGround: true,
    },
  })
  viewer.value.entities.add({
    polygon: {
      hierarchy: Cesium.Cartesian3.fromDegreesArray([105.818, 32.45, 105.827, 32.456, 105.83, 32.456, 105.8234, 32.449]),
      material: Cesium.Color.fromCssColorString('#fff').withAlpha(0.2),
      classificationType: Cesium.ClassificationType.BOTH, // classificationType: ClassificationType.TERRAIN,
      HeightReference: Cesium.HeightReference.NONE,
      clampToGround: true,
    },
  })

  // 标记点位  可点击
  let x = 1
  let flog = true
  data.data.map((list, index) => {
    viewer.value.entities.add({
      id: `area${index}`,
      infoId: list.infoId,
      position: new Cesium.Cartesian3.fromDegrees(list.lng, list.lat),
      billboard: {
        image: '/zuobiao.png',
        scale: 0.5,
        // rotation: Cesium.Math.toRadians(45),
        pixelOffset: new Cesium.Cartesian2(0, 20),
      },
      polyline: {
        positions: Cesium.Cartesian3.fromDegreesArray([105.815, 32.441, 105.811, 32.443, 105.818, 32.45, 105.827, 32.456, 105.83, 32.456, 105.815, 32.441]),
        width: 5,
        material: Cesium.Color.RED.withAlpha(0.5),
        clampToGround: true,
      },
      label: {
        //文字标签
        text: list.text.split('').join('\n'),
        font: '500 30px Helvetica', // 15pt monospace
        scale: 0.5,
        style: Cesium.LabelStyle.FILL,
        fillColor: Cesium.Color.WHITE,
        showBackground: true,
        backgroundColor: new Cesium.Color(228, 76, 76, 1.0),
        // pixelOffset: new Cesium.Cartesian2(0, -64),
        // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 1000000)
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.BASELINE,
      },
    })
  })
  handlePointClick() //添加点的点击事件
  // viewer.value.cesiumWidget.screenSpaceEventHandler.setInputAction(function onLeftClick(movement) {
  //   var pickedObject = viewer.value.scene.pick(movement.position);
  //   if (pickedObject && pickedObject.id && pickedObject.id.label) {
  //     // Handle click event for the label entity
  //     console.log('Clicked on label: ' + pickedObject.id.label.text);
  //   }
  // }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  let handler = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas)
  // 获取当前点击位置的经纬度
  handler.setInputAction(function (event) {
    let ray = viewer.value.camera.getPickRay(event.position)
    let cartesian = viewer.value.scene.globe.pick(ray, viewer.value.scene)
    let cartographic = Cesium.Cartographic.fromCartesian(cartesian)
    let lng = Cesium.Math.toDegrees(cartographic.longitude) // 经度
    let lat = Cesium.Math.toDegrees(cartographic.latitude) // 纬度
    let alt = cartographic.height // 高度
    let coordinate = {
      longitude: Number(lng.toFixed(6)),
      latitude: Number(lat.toFixed(6)),
      altitude: Number(alt.toFixed(2)),
    }
    console.log(coordinate)
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
})
function handlePointClick() {
  handler.value = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas)
  handler.value.setInputAction(function (click) {
    let pick = viewer.value.scene.pick(click.position)
    if (pick && pick.id) {
      viewer.value.entities.values.map((item) => {
        if (pick.id.id && item.id == pick.id.id) {
          // that.viewer._selectedEntity = [];//去除左击之后出现选中的绿框
          if (item.infoId) {
            // router.push({
            //   path: '/specificCommunity',
            //   query: { id: item.infoId }
            // })

            // 点击点之后的操作
            alert(item.label.text)
            // viewer.value.entities.values.forEach(function (entity) {
            //   entity.billboard = undefined;
            //   entity.label = undefined;
            // });
          }
        }
      })
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
  viewer.value.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK) //去除双击放大地图效果
}
function showAll() {
  zhan.value = !zhan.value
  store.dispatch('changeZhan', zhan.value)
}
onUnmounted(() => {
  // handler.value.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
})
</script>
<style scoped lang="scss">
.goback {
  width: 70px;
  height: 28px;
  background: url(/di01.png) no-repeat;
  // background-size: 100% 100%;
  z-index: 2;
  position: absolute;
  left: 150px;
  top: 25px;
  color: white;
  text-align: center;
  font-size: 14px;
  line-height: 28px;
  cursor: pointer;
}

.qiu {
  z-index: 2;
  position: absolute;
  right: 200px;
  top: 20px;
  cursor: pointer;
}

#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.label {
  width: 900px;
  height: 200px;
  font-size: 40px;
  color: red;
  position: absolute;
  z-index: 1;
}

.left {
  z-index: 1;
  width: 442px;
  height: 93%;
  position: absolute;
  left: 0;
  top: 7%;

  .one {
    height: 24%;

    .one_dis {
      width: 100%;
      height: 185px;
      display: flex;
      // justify-content: space-between;

      .dis_ {
        color: #fff;
        width: 25%;
        text-align: center;

        .top {
          font-size: 34px;
          position: relative;
          top: 40px;
          font-family: DIN;
          font-weight: bold;
          color: #ffffff;
          line-height: 45px;

          background: linear-gradient(0deg, rgba(119, 244, 245, 0.99) 1.2939453125%);
        }

        .bottom {
          font-size: 16px;
        }
      }
    }
  }

  .two {
    height: 34%;

    .two_dis {
      height: 293px;
    }
  }

  .three {
    height: 39%;
  }
}

.right {
  z-index: 1;
  width: 442px;
  height: 93%;
  position: absolute;
  right: 0;
  top: 7%;

  .one {
    height: 27%;

    .one_dis {
      height: 220px;
    }
  }

  .two {
    .two_dis {
      height: 560px;
    }
  }

  .three {
    .three_dis {
      height: 287px;
    }
  }
}

.biao {
  width: 229px;
  height: 55px;
  position: absolute;
  left: 856px;
  top: 107px;
  background: url(/di04.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  color: white;
  font-size: 24px;
  text-align: center;
  line-height: 55px;
}

.one {
  height: 25%;
}

.two {
  height: 40%;
}

.three {
  height: 35%;
}

.title {
  height: 50px;
  background: url('/biaoti_di.png') no-repeat;
  background-size: cover;

  span {
    font-size: 26px;
    padding-left: 40px;
    font-weight: 600;
    line-height: 50px;
    color: #fff;
  }
}
</style>
