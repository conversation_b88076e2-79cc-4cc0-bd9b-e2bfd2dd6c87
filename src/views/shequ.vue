<template>
  <div class="goback" @click="goBack" style="font-size: 12px" v-if="isAndroid">
    <span class="backText">&emsp;返回</span>
  </div>
  <div id="cesiumContainer"></div>
</template>
<script setup>
import * as Cesium from 'cesium'
import { onMounted, onUnmounted, ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import img from '/dasanjiao.png'
let viewer = ref(null)
let handler = ref(null)
const router = useRouter()
const timer = ref(null)
let isAndroid = ref(false)
let data = reactive({
  data: [
    {
      lng: 105.817043,
      lat: 32.44637,
      infoId: 1,
      // text: 'A街区网格'
    },
    {
      lng: 105.824314,
      lat: 32.4526,
      infoId: 2,
      // text: 'B街区网格'
    },
  ],
})
onMounted(() => {
   // 检测是否为安卓环境
   isAndroid.value = /android/i.test(navigator.userAgent)
  // const viewer = new Cesium.Viewer('cesiumContainer');
  // let custom = new Cesium.ArcGisMapServerImageryProvider({
  //   url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer'
  // })
  Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.oeQSme1YSkYiiXbr26yD0qSVT1yTIAwNLjDOkcCGg7M'
  viewer.value = new Cesium.Viewer('cesiumContainer', {
    // 稳定版
    geocoder: false, //是否显示地名查找控件
    sceneModePicker: false, //是否显示投影方式控件
    navigationHelpButton: false, //是否显示帮助信息控件
    baseLayerPicker: true, //是否显示图层选择控件
    homeButton: false, //是否显示Home按钮
    fullscreenButton: false, //是否显示全屏按钮
    timeline: false, //时间轴控件
    animation: false, //动画控件
    shouldAnimate: true,
    infoBox: false, //初始化不弹出弹出框
    selectionIndicator: false, //初始化不选中
    baseLayerPicker: false,
    // imageryProvider: custom,
    terrainProvider: Cesium.createWorldTerrain({
      requestVertexNormals: true,
      requestWaterMask: true,
    }),
  })

  viewer.value.cesiumWidget.creditContainer.style.display = 'none' //去cesium logo水印 或 css

  const tile = new Cesium.Cesium3DTileset({
    url: 'https://cdn.zn.nextv.show/zn/tileset.json',
  })
  viewer.value.scene.primitives.add(tile)
  viewer.value.scene.globe.depthTestAgainstTerrain = true
  timer.value = setTimeout(() => {
    viewer.value.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(105.8170876001954, 32.**************, 1000.0),
      orientation: {
        heading: Cesium.Math.toRadians(340.69649430383816), //方向,以弧度为单位的航向角。
        pitch: Cesium.Math.toRadians(-28.671005249524338), //倾斜角度,以弧度为单位的俯仰角。
        roll: 0.0000099180339393996,
      },
      duration: 5,
    })

    let div = document.createElement('div')

    div.id = 1
    div.style.position = 'absolute'
    div.style.width = '240px'
    div.style.height = '240px'
    div.style.textAlign = 'center'
    div.style.fontSize = '30px'
    div.style.color = 'white'
    div.style.fontWeight = 'bold'
    div.style.lineHeight = '50px'
    div.style.cursor = 'pointer'
    let HTMLTable = `
		<div style="background:linear-gradient(to top, #f34e01, #e6403d);border-radius:10px;">上西则南社区</div>
    <img src="${img}" alt="" style="margin-top:10px" id='img'>
    `
    div.innerHTML = HTMLTable
    viewer.value.cesiumWidget.container.appendChild(div)
    let gisPosition = Cesium.Cartesian3.fromDegrees(105.813663, 32.447343, 500.0)

    viewer.value.scene.postRender.addEventListener(() => {
      const canvasHeight = viewer.value.scene.canvas.height
      const windowPosition = new Cesium.Cartesian2()
      Cesium.SceneTransforms.wgs84ToWindowCoordinates(viewer.value.scene, gisPosition, windowPosition)
      div.style.bottom = canvasHeight - windowPosition.y + 'px'
      const elWidth = div.offsetWidth
      div.style.left = windowPosition.x - elWidth / 2 + 'px'
    })
    document.getElementById('img').animate(
      [
        {
          transform: 'translateY(20px)',
        },
      ],
      {
        duration: 3000,
        easing: 'linear',
        iterations: 'Infinity',
        // fill: 'forwards',
        delay: 0,
        // direction: 'alternate'
      }
    )
    div.addEventListener('click', clickFun)

    let handler = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas)
    handler.setInputAction(function (event) {
      getCurrentExtent()
      let ray = viewer.value.camera.getPickRay(event.position)
      let cartesian = viewer.value.scene.globe.pick(ray, viewer.value.scene)
      let cartographic = Cesium.Cartographic.fromCartesian(cartesian)
      let lng = Cesium.Math.toDegrees(cartographic.longitude) // 经度
      let lat = Cesium.Math.toDegrees(cartographic.latitude) // 纬度
      let alt = cartographic.height // 高度
      let coordinate = {
        longitude: Number(lng.toFixed(6)),
        latitude: Number(lat.toFixed(6)),
        altitude: Number(alt.toFixed(2)),
      }
      console.log(coordinate)
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
    let dataZ = [
      [105.818, 32.446155, 4000],
      [105.827, 32.456, 4000],
      [105.83, 32.456, 4000],
      [105.8234, 32.449, 4000],
    ]
    dataZ.forEach((bound) => {
      var entity = createAoiByCesium({
        color: new Cesium.Color(240 / 255, 75 / 255, 14 / 255, 0.5),
        show: true,
        positions: [105.809825, 32.446426, 10, 105.817052, 32.452105, 10, 105.81868, 32.450572, 10, 105.813891, 32.446415, 10, 105.811862, 32.442634, 10, 105.811501, 32.442315, 10, 105.809825, 32.446426, 10], // 格式需要为一维数组[114.112, 22.223, 40, 114.113, 22.224, 40]
        wallHeight: 500,
        hasHeight: true,
      })
      entity.tag = 'areaDsLines'
      if (!window.shiningWalls) {
        window.shiningWalls = []
      }
      window.shiningWalls.push(entity)
    })

    handler.value = new Cesium.ScreenSpaceEventHandler(viewer.value.scene.canvas)
    handler.value.setInputAction(function (movement) {
      //具体事件的实现
      var ellipsoid = viewer.value.scene.globe.ellipsoid
      //捕获椭球体，将笛卡尔二维平面坐标转为椭球体的笛卡尔三维坐标，返回球体表面的点
      var cartesian = viewer.value.camera.pickEllipsoid(movement.endPosition, ellipsoid)
      if (cartesian) {
        //将笛卡尔三维坐标转为地图坐标（弧度）
        var cartographic = viewer.value.scene.globe.ellipsoid.cartesianToCartographic(cartesian)
        //将地图坐标（弧度）转为十进制的度数
        var lat_String = Cesium.Math.toDegrees(cartographic.latitude).toFixed(4)
        var log_String = Cesium.Math.toDegrees(cartographic.longitude).toFixed(4)
        var alti_String = (viewer.value.camera.positionCartographic.height / 1000).toFixed(2)
        if (alti_String >= 1.5) {
          div.style.display = 'none'
        } else {
          div.style.display = 'block'
        }
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE)
  }, 3000)
})

function createAoiByCesium(option) {
  var color = option.color ? option.color : Cesium.Color.RED
  var maxHeight = option.maxHeight ? option.maxHeight : 10
  var minHeight = option.minHeight ? option.minHeight : 1
  var maxHeightArray = []
  var minHeightArray = []

  var c = document.createElement('canvas')
  c.width = 50
  c.height = 50
  var ctx = c.getContext('2d')

  var grd = ctx.createLinearGradient(0, 20, 0, 0)
  grd.addColorStop(0, 'rgba(' + color.red * 255 + ',' + color.green * 255 + ',' + color.blue * 255 + ',1)')
  grd.addColorStop(1, 'rgba(' + color.red * 255 + ',' + color.green * 255 + ',' + color.blue * 255 + ',0)')

  ctx.fillStyle = grd
  ctx.fillRect(0, 0, 50, 50)

  var config = null
  if (option.hasHeight) {
    var positions = []
    option.positions.forEach((x, index) => {
      if (index % 3 == 2) {
        minHeightArray.push(x)
        maxHeightArray.push(x + (isNaN(option.wallHeight) ? 1 : option.wallHeight))
      } else {
        positions.push(x)
      }
    })
    config = {
      wall: {
        show: option.show == false ? false : true,
        positions: Cesium.Cartesian3.fromDegreesArray(positions),
        material: new Cesium.ImageMaterialProperty({
          image: c,
          transparent: true,
        }),
        zIndex: 1000,
      },
    }
  } else {
    for (var i = 0; i < option.positions.length / 2; i++) {
      minHeightArray.push(minHeight)
      maxHeightArray.push(maxHeight)
    }
    config = {
      wall: {
        show: option.show == false ? false : true,
        positions: Cesium.Cartesian3.fromDegreesArray(option.positions),
        material: new Cesium.ImageMaterialProperty({
          image: c,
          transparent: true,
        }),
        zIndex: 1000,
      },
    }
  }
  config.wall.maximumHeights = maxHeightArray
  config.wall.minimumHeights = minHeightArray
  var entity1 = viewer.value.entities.add(config)

  entity1.wall.material.color = new Cesium.CallbackProperty(function (time, x) {
    var alp = 0.5 * Math.abs(Math.sin(new Date().getTime() / 500)) + 0.1
    return color.withAlpha(alp)
  }, false)
  return entity1
}
function clickFun() {
  router.push({ path: '/grid' })
}
// 获取当前相机的俯角等信息  左击即可
function getCurrentExtent() {
  // 范围对象
  var extent = {}
  // 得到当前三维场景
  var scene = viewer.value.scene
  // 得到当前三维场景的椭球体
  var ellipsoid = scene.globe.ellipsoid
  var canvas = scene.canvas
  // canvas左上角
  var car3_lt = viewer.value.camera.pickEllipsoid(new Cesium.Cartesian2(0, 0), ellipsoid)
  // canvas右下角
  var car3_rb = viewer.value.camera.pickEllipsoid(new Cesium.Cartesian2(canvas.width, canvas.height), ellipsoid)
  // 当canvas左上角和右下角全部在椭球体上
  if (car3_lt && car3_rb) {
    var carto_lt = ellipsoid.cartesianToCartographic(car3_lt)
    var carto_rb = ellipsoid.cartesianToCartographic(car3_rb)
    extent.xmin = Cesium.Math.toDegrees(carto_lt.longitude)
    extent.ymax = Cesium.Math.toDegrees(carto_lt.latitude)
    extent.xmax = Cesium.Math.toDegrees(carto_rb.longitude)
    extent.ymin = Cesium.Math.toDegrees(carto_rb.latitude)
  }
  // 当canvas左上角不在但右下角在椭球体上
  else if (!car3_lt && car3_rb) {
    var car3_lt2 = null
    var yIndex = 0
    do {
      // 这里每次10像素递加，一是10像素相差不大，二是为了提高程序运行效率
      yIndex <= canvas.height ? (yIndex += 10) : canvas.height
      car3_lt2 = viewer.value.camera.pickEllipsoid(new Cesium.Cartesian2(0, yIndex), ellipsoid)
    } while (!car3_lt2)
    var carto_lt2 = ellipsoid.cartesianToCartographic(car3_lt2)
    var carto_rb2 = ellipsoid.cartesianToCartographic(car3_rb)
    extent.xmax = Cesium.Math.toDegrees(carto_lt2.longitude)
    extent.ymax = Cesium.Math.toDegrees(carto_lt2.latitude)
    extent.xmin = Cesium.Math.toDegrees(carto_rb2.longitude)
    extent.ymin = Cesium.Math.toDegrees(carto_rb2.latitude)
  }
  // 获取高度
  extent.height = Math.ceil(viewer.value.camera.positionCartographic.height)
  extent.lon = Cesium.Math.toDegrees(viewer.value.camera.positionCartographic.longitude)
  extent.lat = Cesium.Math.toDegrees(viewer.value.camera.positionCartographic.latitude)
  extent.heading = Cesium.Math.toDegrees(viewer.value.camera.heading)
  extent.pitch = Cesium.Math.toDegrees(viewer.value.camera.pitch)
  extent.roll = Cesium.Math.toDegrees(viewer.value.camera.roll)
  //console.log("lon："+extent.lon+"--lat："+extent.lat+"--height："+extent.height+"--heading：" + extent.heading + "--pitch：" + extent.pitch
  //+ "--roll：" + extent.roll);
  //console.log('{"lon":"'+extent.lon+'","lat":"'+extent.lat+'","height":"'+extent.height+'","heading":"'+extent.heading+'","pitch":"'+extent.pitch+'","roll":"'+extent.roll+'"}');
  console.log("{'lon':" + extent.lon + ",'lat':" + extent.lat + ",'height':" + extent.height + ",'heading':" + extent.heading + ",'pitch':" + extent.pitch + ",'roll':" + extent.roll + '}')

  return extent
}
// 在script setup中添加goBack方法
function goBack() {
  try {
    // 尝试使用history API
    if (window && window.history) {
      window.history.back();
    } else if (parent && parent.history) {
      // 尝试使用父窗口的history
      parent.history.back();
    } else {
      // 如果都不可用，使用router
      router.back();
    }
  } catch (e) {
    console.error('导航返回失败:', e);
    // 最后的备选方案
    router.back();
  }
}
onUnmounted(() => {
  viewer.value.destroy()
  viewer.value = null
  clearTimeout(timer.value)
  timer.value = null
})
</script>
<style scoped lang="scss">
#cesiumContainer {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.label {
  width: 220px;
  height: 48px;
  font-size: 30px;
  color: white;
  font-weight: bold;
  position: absolute;
  z-index: 1;
  background: linear-gradient(to top, #f34e01, #ffac00);
  border-radius: 10px;
  text-align: center;
  line-height: 48px;
  top: 20%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
}
.goback {
  width: 60px;
  height: 30px;
  background: url(/di01.png) no-repeat;
  background-size: 100% 100%;
  z-index: 2;
  position: absolute;
  left: 240px;
  top: 37px;
  color: white;
  text-align: center;
  line-height: 30px;
  cursor: pointer;

  .backText {
    vertical-align: middle;
    font-size: 16px;
  }
}
</style>
